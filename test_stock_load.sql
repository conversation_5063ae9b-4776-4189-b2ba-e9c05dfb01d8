-- 测试stock表加载（1.5GB数据）
create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));
create index stock(s_w_id, s_i_id);
load ../../src/test/performance_test/table_data/stock.csv into stock;
select COUNT(*) as count_stock from stock;
