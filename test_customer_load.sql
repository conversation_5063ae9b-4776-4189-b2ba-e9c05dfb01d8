-- 测试customer表加载（337MB数据）
create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));
create index customer(c_w_id, c_d_id, c_id);
load ../../src/test/performance_test/table_data/customer.csv into customer;
select COUNT(*) as count_customer from customer;
