/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "execution_common.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include <fstream>
#include <sstream>
#include <vector>
#include <memory>
#include <cstring>

class LoadExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                    // 表的元数据
    RmFileHandle *fh_;               // 表的数据文件句柄
    std::string tab_name_;           // 表名称
    std::string file_name_;          // 文件名称
    SmManager *sm_manager_;
    std::ifstream csv_file_;         // CSV文件流

    // 批量处理相关
    static constexpr size_t BATCH_SIZE = 10000;  // 批量处理大小
    std::vector<std::unique_ptr<char[]>> batch_records_;  // 批量记录缓冲区
    std::vector<Rid> batch_rids_;                         // 批量记录的RID
    size_t batch_count_;                                  // 当前批次记录数
    bool processing_complete_;                            // 处理是否完成
    Rid rid_;                                            // 最后插入记录的Rid

    // 预分配的缓冲区，避免重复分配
    std::unique_ptr<char[]> line_buffer_;                // 行读取缓冲区
    std::vector<std::unique_ptr<char[]>> index_key_buffers_;

    // 缓存索引句柄，避免重复查找
    std::vector<IxIndexHandle*> index_handles_;

   public:
    LoadExecutor(SmManager *sm_manager, const std::string &tab_name, const std::string &file_name, Context *context) {
        sm_manager_ = sm_manager;
        tab_ = sm_manager_->db_.get_table(tab_name);
        tab_name_ = tab_name;
        file_name_ = file_name;
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
        batch_count_ = 0;
        processing_complete_ = false;

        csv_file_.open(file_name_);
        if (!csv_file_.is_open()) {
            throw FileNotFoundError(file_name_);
        }

        // 跳过CSV文件头
        std::string header_line;
        if (!std::getline(csv_file_, header_line)) {
            throw InternalError("Empty CSV file");
        }

        // 预分配批量处理缓冲区
        size_t record_size = fh_->get_file_hdr().record_size;
        batch_records_.reserve(BATCH_SIZE);
        batch_rids_.reserve(BATCH_SIZE);
        for (size_t i = 0; i < BATCH_SIZE; ++i) {
            batch_records_.push_back(std::make_unique<char[]>(record_size));
        }

        // 预分配行读取缓冲区（假设最大行长度为64KB）
        line_buffer_ = std::make_unique<char[]>(65536);

        // 预分配索引键缓冲区并缓存索引句柄
        index_key_buffers_.reserve(tab_.indexes.size());
        index_handles_.reserve(tab_.indexes.size());
        for (const auto& index : tab_.indexes) {
            index_key_buffers_.push_back(std::make_unique<char[]>(index.col_tot_len));
            auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            index_handles_.push_back(ih);
        }
    }

    ~LoadExecutor() override {
        if (csv_file_.is_open()) {
            csv_file_.close();
        }
    }

    // 高效的CSV行解析，直接解析到记录缓冲区
    bool parse_csv_line_fast(const char* line, size_t line_len, char* record_data) {
        const char* ptr = line;
        const char* end = line + line_len;
        size_t col_idx = 0;

        while (ptr < end && col_idx < tab_.cols.size()) {
            const ColMeta& col = tab_.cols[col_idx];
            const char* field_start = ptr;

            // 找到字段结束位置
            while (ptr < end && *ptr != ',') {
                ptr++;
            }

            size_t field_len = ptr - field_start;

            // 根据列类型直接解析并写入记录缓冲区
            switch (col.type) {
                case TYPE_INT: {
                    int val = 0;
                    bool negative = false;
                    const char* num_ptr = field_start;

                    if (field_len > 0 && *num_ptr == '-') {
                        negative = true;
                        num_ptr++;
                    }

                    while (num_ptr < field_start + field_len && *num_ptr >= '0' && *num_ptr <= '9') {
                        val = val * 10 + (*num_ptr - '0');
                        num_ptr++;
                    }

                    if (negative) val = -val;
                    *(int*)(record_data + col.offset) = val;
                    break;
                }
                case TYPE_FLOAT: {
                    // 使用更高效的float解析
                    float val = 0.0f;
                    if (field_len > 0) {
                        // 简单的float解析实现
                        char temp_buf[32];
                        size_t copy_len = std::min(field_len, sizeof(temp_buf) - 1);
                        memcpy(temp_buf, field_start, copy_len);
                        temp_buf[copy_len] = '\0';
                        val = std::strtof(temp_buf, nullptr);
                    }
                    *(float*)(record_data + col.offset) = val;
                    break;
                }
                case TYPE_STRING: {
                    // 处理字符串字段
                    const char* str_start = field_start;
                    size_t str_len = field_len;

                    // 处理带引号的字符串
                    if (str_len >= 2 && *str_start == '"' && *(field_start + field_len - 1) == '"') {
                        str_start++;
                        str_len -= 2;
                    }

                    // 直接拷贝到记录缓冲区，确保不超过列长度
                    memset(record_data + col.offset, 0, col.len);
                    size_t copy_len = std::min(str_len, (size_t)col.len - 1);
                    if (copy_len > 0) {
                        memcpy(record_data + col.offset, str_start, copy_len);
                    }
                    break;
                }
                default:
                    return false; // 不支持的类型
            }

            col_idx++;
            if (ptr < end) ptr++; // 跳过逗号
        }

        return col_idx == tab_.cols.size();
    }

    // 批量插入记录到数据文件
    void batch_insert_records() {
        if (batch_count_ == 0) return;

        batch_rids_.clear();
        batch_rids_.reserve(batch_count_);

        // 批量插入记录
        for (size_t i = 0; i < batch_count_; ++i) {
            Rid rid = fh_->insert_record(batch_records_[i].get(), context_);
            batch_rids_.push_back(rid);
        }

        // 批量更新索引
        batch_update_indexes();

        // 更新最后插入的RID
        if (!batch_rids_.empty()) {
            rid_ = batch_rids_.back();
        }

        batch_count_ = 0; // 重置批次计数
    }

    // 批量更新索引
    void batch_update_indexes() {
        for (size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
            const auto& index = tab_.indexes[idx];
            auto ih = index_handles_[idx];
            char* key = index_key_buffers_[idx].get();

            // 为当前批次的所有记录更新此索引
            for (size_t i = 0; i < batch_count_; ++i) {
                int offset = 0;

                // 构建索引键
                for (size_t j = 0; j < index.col_num; ++j) {
                    const auto& col = index.cols[j];
                    memcpy(key + offset, batch_records_[i].get() + col.offset, col.len);
                    offset += col.len;
                }

                // 插入索引项
                ih->insert_entry(key, batch_rids_[i], context_->txn_);
            }
        }
    }

    // 处理一批CSV行
    bool process_batch() {
        std::string line;
        batch_count_ = 0;

        // 读取一批数据
        while (batch_count_ < BATCH_SIZE && std::getline(csv_file_, line)) {
            if (line.empty()) continue;

            // 解析CSV行到记录缓冲区
            if (parse_csv_line_fast(line.c_str(), line.length(), batch_records_[batch_count_].get())) {
                batch_count_++;
            }
        }

        // 如果读取到数据，进行批量插入
        if (batch_count_ > 0) {
            batch_insert_records();
            return true;
        }

        return false; // 没有更多数据
    }

    // 实现Next接口，使用批量处理
    std::unique_ptr<RmRecord> Next() override {
        if (processing_complete_) {
            return nullptr;
        }

        try {
            // 批量处理所有数据
            while (process_batch()) {
                // 继续处理下一批
            }

            processing_complete_ = true;

        } catch (const std::exception& e) {
            throw; // 向上层抛出异常
        }

        return nullptr;
    }

    // 返回最后插入记录的Rid
    Rid &rid() override {
        return rid_;
    }
};
