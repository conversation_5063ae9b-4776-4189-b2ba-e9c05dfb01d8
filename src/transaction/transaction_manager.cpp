/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "transaction_manager.h"
#include "record/rm_file_handle.h"
#include "system/sm_manager.h"

std::unordered_map<txn_id_t, Transaction *> TransactionManager::txn_map = {};

/**
 * @description: 事务的开始方法
 * @return {Transaction*} 开始事务的指针
 * @param {Transaction*} txn 事务指针，空指针代表需要创建新事务，否则开始已有事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
Transaction * TransactionManager::begin(Transaction* txn, LogManager* log_manager) {
    // Todo:
    // 1. 判断传入事务参数是否为空指针
    // 2. 如果为空指针，创建新事务
    // 3. 把开始事务加入到全局事务表中
    // 4. 返回当前事务指针
    // 如果需要支持MVCC请在上述过程中添加代码
    
    txn_id_t txn_id = INVALID_TXN_ID;
    timestamp_t timestamp = last_commit_ts_.load();
    
    if(!txn)
    {
        /*创建新事务*/
        txn_id = next_txn_id_.fetch_add(1,std::memory_order_relaxed);
        txn = new Transaction(txn_id);
        if(get_concurrency_mode() == ConcurrencyMode::MVCC)
        {
            /*设置开始时间戳、读取时间戳*/
            txn->set_start_ts(timestamp);//开始时间戳是事务调用begin的时间
            // txn->set_read_ts(timestamp);
        }
    }

    /*设置事务的状态*/
    txn->set_state(TransactionState::GROWING);
    txn->set_txn_mode(true);

    /*写开始日志*/
    BeginLogRecord *begin_log = new BeginLogRecord(txn_id);
    txn->set_prev_lsn(log_manager->add_log_to_buffer(begin_log));

    /*把开始事务加入全局事务表*/
    /*加锁*/
    std::unique_lock<std::mutex> lock(latch_);
    txn_map[txn->get_transaction_id()] = txn;

    /*加入水印槽*/
    if(get_concurrency_mode() == ConcurrencyMode::MVCC)
    {
        running_txns_.AddTxn(txn->get_read_ts());
    }
    return txn;
}

/**
 * @description: 事务的提交方法
 * @param {Transaction*} txn 需要提交的事务
 * @param {LogManager*} log_manager 日志管理器指针
 */
void TransactionManager::commit(Transaction* txn, LogManager* log_manager) {
    // Todo:
    // 1. 如果存在未提交的写操作，提交所有的写操作
    // 2. 释放所有锁
    // 3. 释放事务相关资源，eg.锁集
    // 4. 把事务日志刷入磁盘中
    // 5. 更新事务状态
    // 如果需要支持MVCC请在上述过程中添加代码
    
    /*0.判断事务状态*/
    // assert(txn->get_state() != TransactionState::COMMITTED);
    // assert(txn->get_state() != TransactionState::ABORTED);

    /*1.脏页落盘*/
    /*1.1遍历写集合找出脏页*/
    auto write_set = txn->get_write_set();
    if(write_set)
    {
        // for(auto& ws : *write_set)
        // {
        //     auto rid = ws->GetRid();
        //     auto filehandle = sm_manager_->fhs_.at(ws->GetTableName()).get();
        //     sm_manager_->get_bpm()->flush_page({filehandle->GetFd(), rid.page_no});
        // }
        if(!write_set->empty())
        {
            write_set->clear();
        }
    }
    

    /*2.释放锁有锁*/
    auto lock_set = txn->get_lock_set();
    if(lock_set)
    {
        for(auto& ls : *lock_set)
        {
            lock_manager_->unlock(txn, ls);
        }
        if(!lock_set->empty())
        {
            lock_set->clear();
        }
    }
    

    /*3.释放事务资源*/
    auto idps = txn->get_index_deleted_page_set();
    if(idps)
    {
        if(!idps->empty())
        {
            idps->clear();
        }
    }
    auto dps = txn->get_index_latch_page_set();
    if(dps)
    {
        if(!dps->empty())
        {
            dps->clear();
        }
    }

    /*4.写入提交日志*/
    CommitLogRecord *commit_log = new CommitLogRecord(txn->get_transaction_id());
    txn->set_prev_lsn(log_manager->add_log_to_buffer(commit_log));
    // log_manager->flush_log_to_disk();

    /*5.更新事务状态*/
    txn->set_state(TransactionState::COMMITTED);

    /*6.MVCC*/
    if(get_concurrency_mode() == ConcurrencyMode::MVCC)
    {
        running_txns_.RemoveTxn(txn->get_read_ts());
        /*TODO:其它的MVCC，暂时用不到*/
    }

    /*移除事务*/
    std::scoped_lock lock{latch_};
    txn_map.erase(txn->get_transaction_id());
    // delete txn;

}

/**
 * @description: 事务的终止（回滚）方法
 * @param {Transaction *} txn 需要回滚的事务
 * @param {LogManager} *log_manager 日志管理器指针
 */
void TransactionManager::abort(Transaction * txn, LogManager *log_manager) {
    // Todo:
    // 1. 回滚所有写操作
    // 2. 释放所有锁
    // 3. 清空事务相关资源，eg.锁集
    // 4. 把事务日志刷入磁盘中
    // 5. 更新事务状态
    // 如果需要支持MVCC请在上述过程中添加代码
    
    /*1.回滚写操作*/
    auto write_set = txn->get_write_set();
    if (write_set) {
        // 使用反向迭代器从写集合末尾向开头遍历
        for (auto it = write_set->rbegin(); it != write_set->rend(); ++it) {
            auto& ws = *it;
            LogRecord* log;
            switch (ws->GetWriteType()) {
                case WType::INSERT_TUPLE:
                {
                    insert_abort(txn, *ws, log_manager);
                    // log = new DeleteLogRecord();
                    break;
                }
                case WType::DELETE_TUPLE:
                {
                    delete_abort(txn, *ws, log_manager);
                    // log = new InsertLogRecord();
                    break;
                }
                case WType::UPDATE_TUPLE:
                {
                    update_abort(txn, *ws, log_manager);
                    // log = new UpdateLogRecord();
                    break;
                }
            }
            /*写日志*/
            // txn->set_prev_lsn(log_manager->add_log_to_buffer(log));
        }
        write_set->clear();
    }

    /*2.释放锁有锁*/
    auto lock_set = txn->get_lock_set();
    if(lock_set)
    {
        for(auto& ls : *lock_set)
        {
            lock_manager_->unlock(txn, ls);
        }
    }
    lock_set->clear();

    /*3.释放事务资源*/
    auto idps = txn->get_index_deleted_page_set();
    if(idps)
    {
        idps->clear();
    }
    auto dps = txn->get_index_latch_page_set();
    if(dps)
    {
        dps->clear();
    }

    /*4.写入提交日志*/
    AbortLogRecord *abort_log = new AbortLogRecord(txn->get_transaction_id());
    txn->set_prev_lsn(log_manager->add_log_to_buffer(abort_log));
    // log_manager->flush_log_to_disk();

    /*5.更新事务状态*/
    txn->set_state(TransactionState::ABORTED);

    /*6.MVCC*/
    if(get_concurrency_mode() == ConcurrencyMode::MVCC)
    {
        running_txns_.RemoveTxn(txn->get_read_ts());
        /*TODO:其它的MVCC，暂时用不到*/
    }

    /*移除事务*/
    std::scoped_lock lock{latch_};
    txn_map.erase(txn->get_transaction_id());
    // delete txn;

}

void TransactionManager::insert_abort(Transaction * txn, WriteRecord wr, LogManager *log_manager)
{
    /*1.INSERT的回滚就是将插入的元组删除*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn);
    std::string table_name = wr.GetTableName();
    auto fileHandle = sm_manager_->fhs_.at(table_name).get();
    auto tab_ = sm_manager_->db_.get_table(table_name);
    auto rid = wr.GetRid();
    auto record = fileHandle->get_record(rid, context.get());

    /*2.删除索引*/
    for(size_t idx = 0;idx < tab_.indexes.size();idx++)
    {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
        char *key = new char[index.col_tot_len];
        int offset = 0;
        /*2.1构造主键值*/
        for(size_t i = 0;i < index.col_num; i++)
        {
            memcpy(key+offset, record->data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        /*2.2删除索引*/
        ih->delete_entry(key, txn);
    }

    /*3.删除记录*/
    fileHandle->delete_record(rid, context.get());
}

void TransactionManager::delete_abort(Transaction * txn, WriteRecord wr, LogManager *log_manager)
{
    /*1.删除的回滚就是重新插入*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn);
    std::string table_name = wr.GetTableName();
    auto fileHandle = sm_manager_->fhs_.at(table_name).get();
    auto tab_ = sm_manager_->db_.get_table(table_name);
    auto record = wr.GetRecord();
    
    /*2.不能从wr获取rid,因为此时rid的位置已经被占用*/
    auto rid = fileHandle->insert_record(record.data, context.get());

    /*3.插入索引*/
    for(size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
        char* key = new char[index.col_tot_len];
        int offset = 0;
        for(size_t i = 0; i < index.col_num; ++i) {
            memcpy(key + offset, record.data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        ih->insert_entry(key, rid, txn);
    }
}

void TransactionManager::update_abort(Transaction * txn, WriteRecord wr, LogManager *log_manager)
{
    /*1.更新的回滚就是将旧值写回去*/
    std::shared_ptr<Context> context = std::make_shared<Context>(lock_manager_, log_manager, txn);
    std::string table_name = wr.GetTableName();
    auto fileHandle = sm_manager_->fhs_.at(table_name).get();
    auto tab_ = sm_manager_->db_.get_table(table_name);
    auto rid = wr.GetRid();
    auto new_record = wr.GetRecord();

    /*2.删除旧索引*/
    auto old_record = fileHandle->get_record(rid, context.get());
    for(size_t idx = 0;idx < tab_.indexes.size();idx++)
    {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name,index.cols)).get();
        char *key = new char[index.col_tot_len];
        int offset = 0;
        /*2.1构造主键值*/
        for(size_t i = 0;i < index.col_num; i++)
        {
            memcpy(key+offset, old_record->data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        /*2.2删除索引*/
        ih->delete_entry(key, txn);
    }

    /*3.更新记录*/
    fileHandle->update_record(rid, new_record.data, context.get());

    /*4.插入新索引*/
    for(size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
        auto& index = tab_.indexes[idx];
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(table_name, index.cols)).get();
        char* key = new char[index.col_tot_len];
        int offset = 0;
        for(size_t i = 0; i < index.col_num; ++i) {
            memcpy(key + offset, new_record.data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }
        ih->insert_entry(key, rid, txn);
    }
}